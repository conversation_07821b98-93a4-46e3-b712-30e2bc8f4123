// components/Loading.tsx
import React, { useState, useEffect } from 'react';

interface LoadingProps {
  onLoadingComplete: () => void;
}

const Loading: React.FC<LoadingProps> = ({ onLoadingComplete }) => {
  const [progress, setProgress] = useState(0);
  const [isComplete, setIsComplete] = useState(false);
  const [isZooming, setIsZooming] = useState(false);

  useEffect(() => {
    const duration = 3000; // 3 seconds total loading time
    const interval = 30; // Update every 30ms for smoother animation
    const steps = duration / interval;
    const increment = 100 / steps;

    let currentProgress = 0;

    const timer = setInterval(() => {
      currentProgress += increment;
      
      if (currentProgress >= 100) {
        clearInterval(timer);
        setProgress(100);
        setIsComplete(true);
        
        // Start zoom animation after progress completes
        setTimeout(() => {
          setIsZooming(true);
        }, 300);
        
        // Call onLoadingComplete after zoom animation
        setTimeout(() => {
          onLoadingComplete();
        }, 1300); // 300ms delay + 1000ms zoom animation
        return;
      }
      
      // Add some easing to make it feel more natural
      const eased = easeOutQuart(currentProgress / 100) * 100;
      setProgress(eased);
    }, interval);

    return () => clearInterval(timer);
  }, [onLoadingComplete]);

  // Easing function for smooth animation
  const easeOutQuart = (t: number): number => {
    return 1 - Math.pow(1 - t, 4);
  };

  return (
    <div className={`fixed inset-0 bg-black flex items-center justify-center z-50 transition-all duration-1000 ${
      isZooming ? 'scale-[50] bg-white' : 'scale-100'
    } ${isComplete && !isZooming ? 'opacity-100' : isComplete ? 'opacity-100' : 'opacity-100'}`}>
      <div className="relative w-full max-w-lg px-8">
        {/* Progress Counter */}
        <div className={`absolute bottom-20 left-8 transition-opacity duration-300 ${
          isZooming ? 'opacity-0' : 'opacity-100'
        }`}>
          <span 
            className="text-white font-medium tracking-tight select-none"
            style={{ 
              fontFamily: 'Aeonik-Medium, sans-serif',
              fontSize: 'clamp(4rem, 12vw, 8rem)',
              lineHeight: '0.8',
              fontFeatureSettings: '"tnum" 1'
            }}
          >
            {Math.floor(progress).toString().padStart(3, '0')}
          </span>
        </div>

        {/* Progress Bar Container */}
        <div className="relative w-full h-0.5 bg-gray-700 mx-auto">
          {/* Progress Bar Fill */}
          <div 
            className={`absolute top-0 left-0 h-full bg-white transition-all duration-75 ease-out ${
              isZooming ? 'scale-y-[2000] origin-center' : ''
            }`}
            style={{ 
              width: `${progress}%`,
              transformOrigin: 'left center',
              boxShadow: '0 0 10px rgba(255,255,255,0.3)'
            }}
          />
        </div>

        {/* Loading Text */}
        <div className={`absolute top-20 left-8 transition-opacity duration-300 ${
          isZooming ? 'opacity-0' : 'opacity-100'
        }`}>
          <div 
            className="text-white text-xs font-normal tracking-widest opacity-50 select-none"
            style={{ 
              fontFamily: 'Aeonik-Regular, sans-serif',
              letterSpacing: '0.2em'
            }}
          >
            LOADING
          </div>
        </div>
      </div>

      {/* Subtle background effect */}
      <div className={`absolute inset-0 overflow-hidden pointer-events-none transition-opacity duration-300 ${
        isZooming ? 'opacity-0' : 'opacity-100'
      }`}>
        <div 
          className="absolute inset-0 transition-opacity duration-2000"
          style={{
            background: `linear-gradient(90deg, transparent ${progress - 20}%, rgba(255,255,255,0.03) ${progress}%, transparent ${progress + 20}%)`,
            opacity: progress > 10 ? 1 : 0
          }}
        />
      </div>
    </div>
  );
};

export default Loading;