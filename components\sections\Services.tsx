'use client';

import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import Link from 'next/link';

const Services = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  });

  const services = [
    {
      title: "Esthétique dentaire",
      description: "Retrouver un sourire éclatant de vitalité",
      image: "/api/placeholder/400/300",
      treatments: [
        "Facettes dentaires",
        "Blanchiment des dents",
        "Orthodontie invisible",
        "Sourire gingival"
      ],
      href: "/services/esthetique",
      color: "from-blue-600 to-blue-800"
    },
    {
      title: "Chirurgie buccale",
      description: "Des opérations courantes pour la santé de vos dents",
      image: "/api/placeholder/400/300",
      treatments: [
        "Implants dentaires",
        "Greffe osseuse",
        "Regénération osseuse guidée",
        "Sinus Lift"
      ],
      href: "/services/chirurgie",
      color: "from-teal-600 to-teal-800"
    },
    {
      title: "Soins d'exception",
      description: "Prenez soin de votre dentition",
      image: "/api/placeholder/400/300",
      treatments: [
        "Soin Elone",
        "Soins des caries",
        "Soins des gencives",
        "Dentisterie générale"
      ],
      href: "/services/soins",
      color: "from-purple-600 to-purple-800"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  return (
    <section className="py-20 bg-gray-50" ref={ref}>
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Nos soins et traitements
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Le cabinet dentaire ELONE CLINIC dirigé par le docteur Elhyani propose 
            une large palette de soins et traitements dentaires à Paris.
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="grid grid-cols-1 lg:grid-cols-3 gap-8"
        >
          {services.map((service, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="group"
            >
              <div className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
                {/* Image */}
                <div className="relative h-64 overflow-hidden">
                  <div className={`absolute inset-0 bg-gradient-to-br ${service.color} opacity-90 z-10`} />
                  <img
                    src={service.image}
                    alt={service.title}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 z-20 flex items-center justify-center">
                    <h3 className="text-2xl font-bold text-white text-center px-4">
                      {service.title}
                    </h3>
                  </div>
                </div>

                {/* Content */}
                <div className="p-6">
                  <p className="text-gray-600 mb-6 leading-relaxed">
                    {service.description}
                  </p>

                  {/* Treatments List */}
                  <ul className="space-y-2 mb-6">
                    {service.treatments.map((treatment, treatmentIndex) => (
                      <li key={treatmentIndex} className="flex items-center text-gray-700">
                        <div className="w-2 h-2 bg-blue-600 rounded-full mr-3 flex-shrink-0" />
                        {treatment}
                      </li>
                    ))}
                  </ul>

                  {/* CTA Button */}
                  <Link
                    href={service.href}
                    className="inline-flex items-center justify-center w-full bg-blue-900 text-white py-3 px-6 rounded-full hover:bg-blue-800 transition-colors duration-300 font-semibold"
                  >
                    En savoir plus
                    <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                    </svg>
                  </Link>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center mt-16"
        >
          <Link
            href="/services"
            className="inline-flex items-center bg-blue-900 text-white px-8 py-4 rounded-full hover:bg-blue-800 transition-all duration-300 transform hover:scale-105 font-semibold text-lg shadow-lg"
          >
            Voir tous nos services
            <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </Link>
        </motion.div>
      </div>
    </section>
  );
};

export default Services;
