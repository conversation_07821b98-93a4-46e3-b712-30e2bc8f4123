import React, { useState, useRef, useLayoutEffect } from 'react';
import { motion, useScroll, AnimatePresence } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { ScrollRevealText } from '../hooks/animation/ScrollRevealText';
import { projectsData, Project } from '../data/projects/featured';

// Project Card Component
const ProjectCard: React.FC<{ 
  project: Project; 
  onClick: (project: Project, cardElement: HTMLDivElement) => void 
}> = ({ project, onClick }) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isTitleHovered, setIsTitleHovered] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: false
  });

  const handleClick = () => {
    if (cardRef.current) {
      onClick(project, cardRef.current);
    }
  };

  return (
    <motion.div
      ref={(el) => {
        ref(el);
        cardRef.current = el;
      }}
      className="group cursor-pointer"
      initial={{ opacity: 0, y: 60 }}
      animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 60 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      onClick={handleClick}
    >
      {/* Project Image Container */}
      <motion.div
        className={`relative aspect-[4/3] rounded-3xl overflow-hidden bg-gradient-to-br ${project.bgColor} mb-6`}
        animate={{
          x: isHovered ? [0, -2, 2, -1, 1, 0] : 0,
          y: isHovered ? [0, -1, 1, -2, 2, 0] : 0
        }}
        transition={{
          duration: 0.6,
          ease: "easeInOut",
          times: [0, 0.2, 0.4, 0.6, 0.8, 1]
        }}
      >
        {/* Simulated Project Content */}
        <div className="absolute inset-0 p-8 flex flex-col justify-between">
          {/* Top Section */}
          <div className="flex justify-between items-start">
            <div className="w-12 h-12 bg-white/10 backdrop-blur-sm rounded-xl"></div>
            <div className="text-right">
              <div className="w-20 h-4 bg-white/20 rounded mb-2"></div>
              <div className="w-16 h-3 bg-white/15 rounded"></div>
            </div>
          </div>

          {/* Center Content */}
          <div className="text-center">
            <motion.h3 
              className="text-3xl md:text-4xl font-bold text-white mb-4"
              animate={{
                scale: isHovered ? 1.05 : 1
              }}
              transition={{ duration: 0.3 }}
            >
              {project.title.split(' ')[0]}
            </motion.h3>
            <p className="text-white/80 text-lg">
              {project.title.includes(' ') ? project.title.split(' ').slice(1).join(' ') : ''}
            </p>
          </div>

          {/* Bottom Decorative Elements */}
          <div className="flex justify-between items-end">
            <motion.div 
              className="w-8 h-8 bg-white/20 rounded-lg"
              animate={{
                rotate: isHovered ? 180 : 0
              }}
              transition={{ duration: 0.5 }}
            ></motion.div>
            <div className="space-y-1">
              <div className="w-12 h-2 bg-white/25 rounded"></div>
              <div className="w-8 h-2 bg-white/20 rounded"></div>
              <div className="w-6 h-2 bg-white/15 rounded"></div>
            </div>
          </div>
        </div>

        {/* Hover Overlay */}
        <motion.div
          className="absolute inset-0 bg-black/20 backdrop-blur-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: isHovered ? 1 : 0 }}
          transition={{ duration: 0.3 }}
        />
      </motion.div>

      {/* Skills Tags */}
      <motion.div 
        className="flex flex-wrap gap-2 mb-4"
        initial={{ opacity: 0 }}
        animate={inView ? { opacity: 1 } : { opacity: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        {project.skills.map((skill, index) => (
          <motion.span
            key={skill}
            className="text-xs font-medium text-gray-600 uppercase tracking-wider"
            initial={{ opacity: 0, y: 20 }}
            animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ 
              duration: 0.5, 
              delay: 0.3 + (index * 0.1) 
            }}
          >
            {skill}
            {index < project.skills.length - 1 && (
              <span className="text-gray-400 ml-2">•</span>
            )}
          </motion.span>
        ))}
      </motion.div>

      {/* Project Title */}
      <motion.div
        className="relative flex items-center overflow-hidden"
        onHoverStart={() => setIsTitleHovered(true)}
        onHoverEnd={() => setIsTitleHovered(false)}
      >
        {/* Arrow that appears from left */}
        <motion.div
          className="flex items-center justify-center mr-3"
          initial={{ x: -30, opacity: 0 }}
          animate={{
            x: isTitleHovered ? 0 : -30,
            opacity: isTitleHovered ? 1 : 0
          }}
          transition={{ duration: 0.3, ease: "easeOut" }}
        >
          <svg 
            width="20" 
            height="20" 
            viewBox="0 0 24 24" 
            fill="none" 
            className="stroke-current text-black"
            strokeWidth="2"
          >
            <path d="M5 12h14M12 5l7 7-7 7"/>
          </svg>
        </motion.div>

        {/* Title text that slides right */}
        <motion.div
          className="overflow-hidden"
          animate={{
            x: isTitleHovered ? 15 : 0
          }}
          transition={{ duration: 0.3, ease: "easeOut" }}
        >
          <ScrollRevealText
            className="text-4xl md:text-5xl font-bold text-black"
            stagger={0.03}
            duration={0.5}
            threshold={0.3}
            delay={0.4}
          >
            {project.title}
          </ScrollRevealText>
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

// Project Page Component
const ProjectPage: React.FC<{ 
  project: Project; 
  onClose: () => void;
  cardBounds: DOMRect | null;
}> = ({ project, onClose, cardBounds }) => {
  const [showContent, setShowContent] = useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => setShowContent(true), 400);
    return () => clearTimeout(timer);
  }, []);

  const handleBackClick = () => {
    setShowContent(false);
    setTimeout(onClose, 200);
  };

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 z-50 bg-gray-50"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      >
        {/* Navigation Bar */}
        <motion.nav
          className="fixed top-0 left-0 right-0 z-60 bg-white/80 backdrop-blur-lg border-b border-gray-200"
          initial={{ y: -100, opacity: 0 }}
          animate={{ y: showContent ? 0 : -100, opacity: showContent ? 1 : 0 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
        >
          <div className="max-w-7xl mx-auto px-6 lg:px-8 py-4 flex items-center justify-between">
            <motion.button
              onClick={handleBackClick}
              className="flex items-center space-x-2 text-gray-600 hover:text-black transition-colors"
              whileHover={{ x: -5 }}
              transition={{ duration: 0.2 }}
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M19 12H5M12 19l-7-7 7-7"/>
              </svg>
              <span className="font-medium">Back to Featured Work</span>
            </motion.button>
            
            <div className="text-sm font-medium text-gray-500 uppercase tracking-wider">
              Project Details
            </div>
          </div>
        </motion.nav>

        {/* Transitioning Card */}
        <motion.div
          className={`fixed bg-gradient-to-br ${project.bgColor} rounded-3xl overflow-hidden`}
          initial={cardBounds ? {
            left: cardBounds.left,
            top: cardBounds.top,
            width: cardBounds.width,
            height: cardBounds.height * 0.75, // Aspect ratio adjustment
          } : {
            left: '50%',
            top: '50%',
            width: 400,
            height: 300,
            x: '-50%',
            y: '-50%'
          }}
          animate={{
            left: 0,
            top: 0,
            width: '100vw',
            height: '100vh',
            x: 0,
            y: 0,
            borderRadius: 0
          }}
          exit={cardBounds ? {
            left: cardBounds.left,
            top: cardBounds.top,
            width: cardBounds.width,
            height: cardBounds.height * 0.75,
            borderRadius: 24,
            x: 0,
            y: 0
          } : {
            left: '50%',
            top: '50%',
            width: 400,
            height: 300,
            x: '-50%',
            y: '-50%',
            borderRadius: 24
          }}
          transition={{ 
            duration: 0.8, 
            ease: [0.25, 0.46, 0.45, 0.94]
          }}
        >
          {/* Hero Section */}
          <div className="relative h-screen flex flex-col justify-center items-center p-12 text-center">
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ 
                opacity: showContent ? 1 : 0, 
                y: showContent ? 0 : 50 
              }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              <motion.h1 
                className="text-6xl md:text-8xl lg:text-9xl font-bold text-white mb-8"
                initial={{ opacity: 0, y: 30 }}
                animate={{ 
                  opacity: showContent ? 1 : 0, 
                  y: showContent ? 0 : 30 
                }}
                transition={{ duration: 0.8, delay: 0.8 }}
              >
                {project.title}
              </motion.h1>
              
              <motion.p 
                className="text-xl md:text-2xl text-white/90 max-w-4xl mb-12"
                initial={{ opacity: 0, y: 30 }}
                animate={{ 
                  opacity: showContent ? 1 : 0, 
                  y: showContent ? 0 : 30 
                }}
                transition={{ duration: 0.8, delay: 1.0 }}
              >
                {project.description}
              </motion.p>

              <motion.div 
                className="flex flex-wrap justify-center gap-4 mb-12"
                initial={{ opacity: 0, y: 30 }}
                animate={{ 
                  opacity: showContent ? 1 : 0, 
                  y: showContent ? 0 : 30 
                }}
                transition={{ duration: 0.8, delay: 1.2 }}
              >
                {project.skills.map((skill, index) => (
                  <motion.span
                    key={skill}
                    className="px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-white font-medium text-sm"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ 
                      opacity: showContent ? 1 : 0, 
                      scale: showContent ? 1 : 0.8 
                    }}
                    transition={{ 
                      duration: 0.5, 
                      delay: 1.4 + (index * 0.1) 
                    }}
                  >
                    {skill}
                  </motion.span>
                ))}
              </motion.div>

              <motion.button
                className="bg-white text-black px-8 py-4 rounded-full font-semibold text-lg hover:bg-white/90 transition-colors"
                initial={{ opacity: 0, y: 30 }}
                animate={{ 
                  opacity: showContent ? 1 : 0, 
                  y: showContent ? 0 : 30 
                }}
                transition={{ duration: 0.8, delay: 1.6 }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                View Live Project
              </motion.button>
            </motion.div>

            {/* Animated Background Elements */}
            <motion.div
              className="absolute top-1/4 left-1/4 w-20 h-20 bg-white/10 rounded-3xl"
              animate={{
                y: [0, -30, 0],
                rotate: [0, 180, 360],
                scale: [1, 1.2, 1]
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
            <motion.div
              className="absolute bottom-1/3 right-1/4 w-16 h-16 bg-white/15 rounded-full"
              animate={{
                y: [0, 25, 0],
                x: [0, 15, 0],
                opacity: [0.5, 1, 0.5]
              }}
              transition={{
                duration: 6,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          </div>
        </motion.div>

        {/* Content Sections */}
        <motion.div
          className="relative z-10 bg-white min-h-screen pt-32"
          initial={{ opacity: 0 }}
          animate={{ opacity: showContent ? 1 : 0 }}
          transition={{ duration: 0.6, delay: 1.0 }}
        >
          <div className="max-w-7xl mx-auto px-6 lg:px-8 py-20">
            {/* Project Details Section */}
            <motion.div
              className="grid grid-cols-1 lg:grid-cols-2 gap-16 mb-20"
              initial={{ opacity: 0, y: 50 }}
              animate={{ 
                opacity: showContent ? 1 : 0, 
                y: showContent ? 0 : 50 
              }}
              transition={{ duration: 0.8, delay: 1.4 }}
            >
              <div>
                <h2 className="text-4xl font-bold mb-8">Project Overview</h2>
                <p className="text-gray-600 text-lg leading-relaxed mb-6">
                  This project represents a culmination of innovative design thinking and 
                  cutting-edge development practices. Our team worked closely with the client 
                  to deliver a solution that not only meets their immediate needs but also 
                  positions them for future growth.
                </p>
                <p className="text-gray-600 text-lg leading-relaxed">
                  Through careful consideration of user experience, performance optimization, 
                  and scalable architecture, we created a digital experience that truly 
                  stands out in today's competitive landscape.
                </p>
              </div>
              
              <div className="space-y-8">
                <div>
                  <h3 className="text-xl font-semibold mb-4">Role</h3>
                  <p className="text-gray-600">Full-Stack Development & Design</p>
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-4">Duration</h3>
                  <p className="text-gray-600">6 Months</p>
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-4">Technologies</h3>
                  <div className="flex flex-wrap gap-2">
                    {project.skills.map(skill => (
                      <span 
                        key={skill}
                        className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Image Gallery Section */}
            <motion.div
              className="mb-20"
              initial={{ opacity: 0, y: 50 }}
              animate={{ 
                opacity: showContent ? 1 : 0, 
                y: showContent ? 0 : 50 
              }}
              transition={{ duration: 0.8, delay: 1.6 }}
            >
              <h2 className="text-4xl font-bold mb-12 text-center">Project Gallery</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {[1, 2, 3, 4].map((index) => (
                  <motion.div
                    key={index}
                    className={`aspect-[4/3] rounded-3xl bg-gradient-to-br ${project.bgColor} opacity-20`}
                    whileHover={{ scale: 1.02 }}
                    transition={{ duration: 0.3 }}
                  />
                ))}
              </div>
            </motion.div>

            {/* Next Project CTA */}
            <motion.div
              className="text-center py-20"
              initial={{ opacity: 0, y: 50 }}
              animate={{ 
                opacity: showContent ? 1 : 0, 
                y: showContent ? 0 : 50 
              }}
              transition={{ duration: 0.8, delay: 1.8 }}
            >
              <h2 className="text-4xl font-bold mb-8">Ready to start your project?</h2>
              <motion.button
                className="bg-black text-white px-12 py-4 rounded-full font-semibold text-lg hover:bg-gray-800 transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Get In Touch
              </motion.button>
            </motion.div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

// Main Featured Work Component
const FeaturedWork: React.FC = () => {
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [cardBounds, setCardBounds] = useState<DOMRect | null>(null);
  const containerRef = useRef(null);
  
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });

  const [headerRef, headerInView] = useInView({
    threshold: 0.1,
    triggerOnce: false
  });

  const handleProjectClick = (project: Project, cardElement: HTMLDivElement) => {
    const bounds = cardElement.getBoundingClientRect();
    setCardBounds(bounds);
    setSelectedProject(project);
    
    // Update URL without page reload
    const projectSlug = project.title.toLowerCase().replace(/\s+/g, '-');
    window.history.pushState({}, '', `/projects/${projectSlug}`);
  };

  const handleCloseProject = () => {
    setSelectedProject(null);
    setCardBounds(null);
    
    // Return to original URL
    window.history.pushState({}, '', '/');
  };

  return (
    <>
      <section 
        ref={containerRef}
        className="relative min-h-screen bg-gray-50 py-20 overflow-hidden"
      >
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          {/* Header */}
          <div ref={headerRef} className="mb-20">
            <div className="flex flex-col lg:flex-row justify-between items-start lg:items-end mb-8">
              <ScrollRevealText
                className="text-6xl md:text-7xl lg:text-8xl font-bold text-black mb-4 lg:mb-0"
                stagger={0.04}
                duration={0.6}
                threshold={0.3}
              >
                Featured Work
              </ScrollRevealText>
              
              <motion.div 
                className="max-w-md"
                initial={{ opacity: 0, x: 50 }}
                animate={headerInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 50 }}
                transition={{ duration: 0.8, delay: 0.6 }}
              >
                <ScrollRevealText
                  className="text-sm uppercase tracking-wider text-gray-600 mb-4"
                  stagger={0.02}
                  duration={0.4}
                  threshold={0.5}
                  delay={0.8}
                >
                  A SELECTION OF OUR MOST PASSIONATELY
                </ScrollRevealText>
                <ScrollRevealText
                  className="text-sm uppercase tracking-wider text-gray-600"
                  stagger={0.02}
                  duration={0.4}
                  threshold={0.5}
                  delay={1.0}
                >
                  CRAFTED WORKS WITH FORWARD-THINKING
                </ScrollRevealText>
                <ScrollRevealText
                  className="text-sm uppercase tracking-wider text-gray-600"
                  stagger={0.02}
                  duration={0.4}
                  threshold={0.5}
                  delay={1.2}
                >
                  CLIENTS AND FRIENDS OVER THE YEARS.
                </ScrollRevealText>
              </motion.div>
            </div>
          </div>

          {/* Projects Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16">
            {projectsData.map((project) => (
              <ProjectCard
                key={project.id}
                project={project}
                onClick={handleProjectClick}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Project Page */}
      {selectedProject && (
        <ProjectPage 
          project={selectedProject} 
          onClose={handleCloseProject}
          cardBounds={cardBounds}
        />
      )}
    </>
  );
};

export default FeaturedWork;