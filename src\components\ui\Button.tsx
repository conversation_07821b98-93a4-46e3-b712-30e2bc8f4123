'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';

// Reusable Scroll-Triggered Text Reveal Component (copied from original)
const ScrollRevealText = ({ 
  children, 
  className = "",
  href = null,
  stagger = 0.025,
  duration = 0.25,
  threshold = 0.1,
  triggerOnce = false,
  delay = 0,
  hoverEffect = false,
  onClick = null
}) => {
  const [ref, inView] = useInView({
    threshold,
    triggerOnce
  });

  const Component = href ? motion.a : motion.div;
  const componentProps = href ? { href } : onClick ? { onClick } : {};

  return (
    <Component
      ref={ref}
      initial="initial"
      animate={inView ? "visible" : "initial"}
      whileHover={hoverEffect ? "hovered" : undefined}
      className={`relative block overflow-hidden ${className}`}
      {...componentProps}
    >
      {/* First layer - visible text that slides up on scroll reveal */}
      <div>
        {children.split("").map((char, i) => (
          <motion.span
            key={`first-${i}`}
            variants={{
              initial: {
                y: "100%",
              },
              visible: {
                y: 0,
              },
              hovered: hoverEffect ? {
                y: "-100%",
              } : {}
            }}
            transition={{
              duration: duration,
              ease: "easeInOut",
              delay: delay + (stagger * i),
            }}
            className="inline-block"
            style={{ 
              whiteSpace: char === " " ? "pre" : "normal",
            }}
          >
            {char}
          </motion.span>
        ))}
      </div>

      {/* Second layer - hover effect (only if hoverEffect is true) */}
      {hoverEffect && (
        <div className="absolute inset-0">
          {children.split("").map((char, i) => (
            <motion.span
              key={`second-${i}`}
              variants={{
                initial: {
                  y: "100%",
                },
                visible: {
                  y: "100%",
                },
                hovered: {
                  y: 0,
                }
              }}
              transition={{
                duration: duration,
                ease: "easeInOut",
                delay: stagger * i,
              }}
              className="inline-block"
              style={{ 
                whiteSpace: char === " " ? "pre" : "normal",
              }}
            >
              {char}
            </motion.span>
          ))}
        </div>
      )}
    </Component>
  );
};

// Animated Button Component
const AnimatedButton = ({
  children,
  onClick = () => {},
  href = null,
  variant = "primary", // primary, secondary, outline
  size = "medium", // small, medium, large
  showArrow = true,
  showDot = true,
  className = "",
  disabled = false,
  animationDelay = 0,
  fillColor = "#2563eb", // blue-600
  textColor = "#000000",
  hoverTextColor = "#ffffff",
  style = {}
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [ref, inView] = useInView({
    threshold: 0.8,
    triggerOnce: true
  });

  // Size variants
  const sizeClasses = {
    small: "px-6 py-2 text-sm",
    medium: "px-8 py-4 text-lg", 
    large: "px-10 py-5 text-xl"
  };

  // Variant styles
  const variantClasses = {
    primary: "bg-white border border-gray-300 rounded-full",
    secondary: "bg-gray-100 border border-gray-400 rounded-full", 
    outline: "bg-transparent border-2 border-gray-800 rounded-full"
  };

  const Component = href ? motion.a : motion.button;
  const componentProps = href ? { href } : { onClick };

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 30 }}
      animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
      transition={{ duration: 0.6, delay: animationDelay }}
    >
      <Component
        className={`relative ${variantClasses[variant]} ${sizeClasses[size]} flex items-center gap-3 font-medium overflow-hidden group cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed ${className}`}
        style={{ 
          fontFamily: 'Aeonik-Medium, sans-serif', 
          minWidth: size === 'large' ? '200px' : size === 'medium' ? '160px' : '120px',
          color: textColor,
          ...style 
        }}
        onHoverStart={() => !disabled && setIsHovered(true)}
        onHoverEnd={() => !disabled && setIsHovered(false)}
        whileHover={!disabled ? { scale: 1.02 } : {}}
        whileTap={!disabled ? { scale: 0.98 } : {}}
        transition={{ duration: 0.3 }}
        disabled={disabled}
        {...componentProps}
      >
        {/* Fast fill background */}
        <motion.div
          className="absolute inset-0 rounded-full"
          style={{ backgroundColor: fillColor }}
          initial={{ x: '-100%' }}
          animate={{
            x: isHovered && !disabled ? '0%' : '-100%',
          }}
          transition={{ duration: 0.2, ease: "easeOut" }}
        />
        
        {/* Dot indicator */}
        {showDot && (
          <motion.div 
            className="relative z-10 w-2 h-2 rounded-full"
            style={{ backgroundColor: textColor }}
            animate={{
              backgroundColor: isHovered && !disabled ? hoverTextColor : textColor
            }}
            transition={{ duration: 0.2 }}
          />
        )}
        
        {/* Button text with reveal animation */}
        <div className="relative z-10 overflow-hidden">
          <ScrollRevealText
            stagger={0.03}
            duration={0.4}
            delay={animationDelay + 0.2}
            triggerOnce={true}
            threshold={0.8}
            className={isHovered && !disabled ? 'text-white' : ''}
          >
            {children}
          </ScrollRevealText>
        </div>

        {/* Arrow appearing from right */}
        {showArrow && (
          <motion.div
            className="relative z-10 flex items-center justify-center"
            initial={{ x: 30, opacity: 0 }}
            animate={{
              x: isHovered && !disabled ? 0 : 30,
              opacity: isHovered && !disabled ? 1 : 0
            }}
            transition={{ duration: 0.2, ease: "easeOut" }}
          >
            <svg 
              width={size === 'large' ? '24' : '20'} 
              height={size === 'large' ? '24' : '20'} 
              viewBox="0 0 24 24" 
              fill="none" 
              className="stroke-current"
              strokeWidth="2"
            >
              <motion.path
                d="M5 12h14M12 5l7 7-7 7"
                animate={{
                  stroke: isHovered && !disabled ? hoverTextColor : textColor
                }}
                transition={{ duration: 0.2 }}
              />
            </svg>
          </motion.div>
        )}
      </Component>
    </motion.div>
  );
};

// Pre-configured button variants for common use cases
export const PrimaryButton = (props) => (
  <AnimatedButton variant="primary" {...props} />
);

export const SecondaryButton = (props) => (
  <AnimatedButton 
    variant="secondary" 
    fillColor="#374151" 
    {...props} 
  />
);

export const OutlineButton = (props) => (
  <AnimatedButton 
    variant="outline" 
    fillColor="#1f2937"
    textColor="#1f2937"
    {...props} 
  />
);

// CTA Button with custom styling
export const CTAButton = (props) => (
  <AnimatedButton
    variant="primary"
    size="large"
    fillColor="#dc2626" // red-600
    textColor="#000000"
    hoverTextColor="#ffffff"
    {...props}
  />
);

export default AnimatedButton;