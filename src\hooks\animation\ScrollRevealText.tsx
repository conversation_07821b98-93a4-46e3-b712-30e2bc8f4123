import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';

interface ScrollRevealTextProps {
  children: string;
  className?: string;
  stagger?: number;
  duration?: number;
  threshold?: number;
  triggerOnce?: boolean;
  delay?: number;
  ease?: string | number[];
}

export const ScrollRevealText: React.FC<ScrollRevealTextProps> = ({ 
  children, 
  className = "",
  stagger = 0.025,
  duration = 0.25,
  threshold = 0.1,
  triggerOnce = false,
  delay = 0,
  ease = "easeInOut"
}) => {
  const [ref, inView] = useInView({
    threshold,
    triggerOnce
  });

  return (
    <div
      ref={ref}
      className={`relative block overflow-hidden ${className}`}
    >
      <div>
        {children.split("").map((char, i) => (
          <motion.span
            key={`char-${i}`}
            variants={{
              initial: {
                y: "100%",
              },
              visible: {
                y: 0,
              }
            }}
            initial="initial"
            animate={inView ? "visible" : "initial"}
            transition={{
              duration: duration,
              ease: ease,
              delay: delay + (stagger * i),
            }}
            className="inline-block"
            style={{ 
              whiteSpace: char === " " ? "pre" : "normal",
            }}
          >
            {char}
          </motion.span>
        ))}
      </div>
    </div>
  );
};