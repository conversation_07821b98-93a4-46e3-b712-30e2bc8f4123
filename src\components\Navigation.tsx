'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';

// Reusable Scroll-Triggered Text Reveal Component (copied from original)
const ScrollRevealText = ({ 
  children, 
  className = "",
  href = null,
  stagger = 0.025,
  duration = 0.25,
  threshold = 0.1,
  triggerOnce = false,
  delay = 0,
  hoverEffect = false,
  onClick = null
}) => {
  const [ref, inView] = useInView({
    threshold,
    triggerOnce
  });

  const Component = href ? motion.a : motion.div;
  const componentProps = href ? { href } : onClick ? { onClick } : {};

  return (
    <Component
      ref={ref}
      initial="initial"
      animate={inView ? "visible" : "initial"}
      whileHover={hoverEffect ? "hovered" : undefined}
      className={`relative block overflow-hidden ${className}`}
      {...componentProps}
    >
      {/* First layer - visible text that slides up on scroll reveal */}
      <div>
        {children.split("").map((char, i) => (
          <motion.span
            key={`first-${i}`}
            variants={{
              initial: {
                y: "100%",
              },
              visible: {
                y: 0,
              },
              hovered: hoverEffect ? {
                y: "-100%",
              } : {}
            }}
            transition={{
              duration: duration,
              ease: "easeInOut",
              delay: delay + (stagger * i),
            }}
            className="inline-block"
            style={{ 
              whiteSpace: char === " " ? "pre" : "normal",
            }}
          >
            {char}
          </motion.span>
        ))}
      </div>

      {/* Second layer - hover effect (only if hoverEffect is true) */}
      {hoverEffect && (
        <div className="absolute inset-0">
          {children.split("").map((char, i) => (
            <motion.span
              key={`second-${i}`}
              variants={{
                initial: {
                  y: "100%",
                },
                visible: {
                  y: "100%",
                },
                hovered: {
                  y: 0,
                }
              }}
              transition={{
                duration: duration,
                ease: "easeInOut",
                delay: stagger * i,
              }}
              className="inline-block"
              style={{ 
                whiteSpace: char === " " ? "pre" : "normal",
              }}
            >
              {char}
            </motion.span>
          ))}
        </div>
      )}
    </Component>
  );
};

// Navigation Component
const Navigation = ({ 
  logo = "PORTFOLIO",
  navItems = [
    { label: "HOME", href: "#home" },
    { label: "ABOUT", href: "#about" },
    { label: "WORK", href: "#work" },
    { label: "CONTACT", href: "#contact" }
  ],
  className = "",
  sticky = true
}) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <motion.nav 
      className={`w-full z-50 ${sticky ? 'sticky top-0' : ''} bg-white/95 backdrop-blur-sm border-b border-gray-200 ${className}`}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
    >
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        <div className="flex items-center justify-between h-20">
          
          {/* Logo */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <ScrollRevealText
              href="#"
              className="text-2xl font-bold text-black"
              style={{ fontFamily: 'Aeonik-Medium, sans-serif' }}
              stagger={0.05}
              duration={0.4}
              delay={0.3}
              triggerOnce={true}
            >
              {logo}
            </ScrollRevealText>
          </motion.div>

          {/* Desktop Navigation */}
          <motion.div 
            className="hidden md:flex items-center space-x-8"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            {navItems.map((item, index) => (
              <ScrollRevealText
                key={item.label}
                href={item.href}
                className="text-sm font-medium text-gray-700 hover:text-black transition-colors cursor-pointer"
                style={{ fontFamily: 'Aeonik-Regular, sans-serif' }}
                hoverEffect={true}
                stagger={0.03}
                duration={0.3}
                delay={0.5 + (index * 0.1)}
                triggerOnce={true}
              >
                {item.label}
              </ScrollRevealText>
            ))}
          </motion.div>

          {/* Mobile Menu Button */}
          <motion.button
            className="md:hidden relative w-8 h-8 flex flex-col justify-center items-center"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <motion.span
              className="w-6 h-0.5 bg-black absolute"
              animate={{
                rotate: isMenuOpen ? 45 : 0,
                y: isMenuOpen ? 0 : -4
              }}
              transition={{ duration: 0.2 }}
            />
            <motion.span
              className="w-6 h-0.5 bg-black absolute"
              animate={{
                opacity: isMenuOpen ? 0 : 1
              }}
              transition={{ duration: 0.2 }}
            />
            <motion.span
              className="w-6 h-0.5 bg-black absolute"
              animate={{
                rotate: isMenuOpen ? -45 : 0,
                y: isMenuOpen ? 0 : 4
              }}
              transition={{ duration: 0.2 }}
            />
          </motion.button>
        </div>

        {/* Mobile Menu */}
        <motion.div
          className="md:hidden overflow-hidden"
          initial={{ height: 0 }}
          animate={{ height: isMenuOpen ? 'auto' : 0 }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
        >
          <div className="py-4 space-y-4">
            {navItems.map((item, index) => (
              <motion.div
                key={item.label}
                initial={{ opacity: 0, x: -20 }}
                animate={{ 
                  opacity: isMenuOpen ? 1 : 0,
                  x: isMenuOpen ? 0 : -20
                }}
                transition={{ 
                  duration: 0.3, 
                  delay: isMenuOpen ? index * 0.1 : 0 
                }}
              >
                <ScrollRevealText
                  href={item.href}
                  className="block text-lg font-medium text-gray-700 hover:text-black transition-colors py-2"
                  style={{ fontFamily: 'Aeonik-Regular, sans-serif' }}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.label}
                </ScrollRevealText>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </motion.nav>
  );
};

export default Navigation;