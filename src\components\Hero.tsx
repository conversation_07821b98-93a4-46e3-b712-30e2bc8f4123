'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Pool from './Pool';

const Hero = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isLetsTalkHovered, setIsLetsTalkHovered] = useState(false);
  const [isMenuHovered, setIsMenuHovered] = useState(false);
  const [isSoundOn, setIsSoundOn] = useState(true);

  return (
    <div className="relative min-h-screen bg-gray-50 overflow-hidden">
      {/* Navigation */}
      <nav className="relative z-50 flex items-start justify-between p-6 lg:p-8">
        {/* Left side - LUSION and Arrow */}
        <div className="flex items-center gap-4">
          <h1 className="text-2xl lg:text-3xl font-medium text-black" style={{ fontFamily: 'Aeonik-Medium, sans-serif' }}>
            LUSION
          </h1>
          {/* Arrow Icon */}
          <motion.div 
            className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center cursor-pointer"
            whileHover={{ scale: 1.1 }}
            transition={{ duration: 0.2 }}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M7 17L17 7M17 7H7M17 7V17"/>
            </svg>
          </motion.div>
        </div>

        {/* Center - Hero Text aligned with navigation */}
        <div className="flex-1 max-w-xl mx-8">
          <motion.h2 
            className="text-4xl lg:text-4xl xl:text-4xl leading-tight text-black"
            style={{ fontFamily: 'Aeonik-Mediem, sans-serif' }}
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            We help brands create digital experiences that connect with their audience
          </motion.h2>
        </div>

        {/* Right side - Buttons */}
        <div className="flex items-center gap-4">
          {/* Sound Switch Button */}
          <motion.button
            className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center cursor-pointer"
            onClick={() => setIsSoundOn(!isSoundOn)}
            whileHover={{ scale: 1.1 }}
            transition={{ duration: 0.2 }}
          >
            {isSoundOn ? (
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon>
                <path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"></path>
              </svg>
            ) : (
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon>
                <line x1="23" y1="9" x2="17" y2="15"></line>
                <line x1="17" y1="9" x2="23" y2="15"></line>
              </svg>
            )}
          </motion.button>

          {/* Let's Talk Button */}
          <motion.button
            className="relative bg-gray-800 text-white px-6 py-3 rounded-full flex items-center gap-3 text-sm font-medium overflow-hidden"
            style={{ fontFamily: 'Aeonik-Medium, sans-serif' }}
            onHoverStart={() => setIsLetsTalkHovered(true)}
            onHoverEnd={() => setIsLetsTalkHovered(false)}
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
          >
            {/* Blue background slide */}
            <motion.div
              className="absolute inset-0 bg-blue-600"
              initial={{ x: "-100%" }}
              animate={{ x: isLetsTalkHovered ? "0%" : "-100%" }}
              transition={{ duration: 0.4, ease: "easeInOut" }}
            />
            
            {/* Arrow from left */}
            <motion.div
              className="relative z-10"
              initial={{ x: -20, opacity: 0 }}
              animate={{ 
                x: isLetsTalkHovered ? 0 : -20, 
                opacity: isLetsTalkHovered ? 1 : 0 
              }}
              transition={{ duration: 0.3, ease: "easeOut" }}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M5 12h14M12 5l7 7-7 7"/>
              </svg>
            </motion.div>
            
            <span className="relative z-10">LET'S TALK</span>
            
            <motion.div
              className="relative z-10 w-2 h-2 bg-white rounded-full"
              animate={{ scale: isLetsTalkHovered ? 1.2 : 1 }}
              transition={{ duration: 0.3 }}
            />
          </motion.button>

          {/* Menu Button */}
          <motion.button 
            className="bg-gray-200 hover:bg-gray-300 text-black px-6 py-3 rounded-full text-sm font-medium transition-colors duration-300 overflow-hidden relative"
            style={{ fontFamily: 'Aeonik-Medium, sans-serif' }}
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            onHoverStart={() => setIsMenuHovered(true)}
            onHoverEnd={() => setIsMenuHovered(false)}
          >
            <motion.span
              animate={{ y: isMenuHovered ? -30 : 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="block"
            >
              MENU ••
            </motion.span>
            <motion.span
              className="absolute inset-0 flex items-center justify-center"
              animate={{ y: isMenuHovered ? 0 : 30 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
            >
              CLOSE ⋮
            </motion.span>
          </motion.button>
        </div>
      </nav>

      {/* Menu Overlay */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            className="fixed inset-0 z-40 bg-white bg-opacity-95 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex flex-col justify-center items-start px-6 lg:px-8 h-full">
              {/* Navigation Menu */}
              <motion.div
                className="bg-white rounded-3xl p-8 shadow-lg mb-8 w-full max-w-md"
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 50 }}
                transition={{ duration: 0.4, delay: 0.1 }}
              >
                <nav className="space-y-8">
                  <motion.div 
                    className="flex items-center justify-between"
                    whileHover={{ x: 10 }}
                    transition={{ duration: 0.2 }}
                  >
                    <span className="text-2xl font-medium" style={{ fontFamily: 'Aeonik-Medium, sans-serif' }}>
                      HOME
                    </span>
                    <div className="w-2 h-2 bg-black rounded-full"></div>
                  </motion.div>
                  
                  <motion.div 
                    className="text-2xl font-medium cursor-pointer"
                    style={{ fontFamily: 'Aeonik-Medium, sans-serif' }}
                    whileHover={{ x: 10 }}
                    transition={{ duration: 0.2 }}
                  >
                    ABOUT US
                  </motion.div>
                  
                  <motion.div 
                    className="text-2xl font-medium cursor-pointer"
                    style={{ fontFamily: 'Aeonik-Medium, sans-serif' }}
                    whileHover={{ x: 10 }}
                    transition={{ duration: 0.2 }}
                  >
                    PROJECTS
                  </motion.div>
                  
                  <motion.div 
                    className="text-2xl font-medium cursor-pointer"
                    style={{ fontFamily: 'Aeonik-Medium, sans-serif' }}
                    whileHover={{ x: 10 }}
                    transition={{ duration: 0.2 }}
                  >
                    CONTACT
                  </motion.div>
                </nav>
              </motion.div>

              {/* Newsletter Subscription */}
              <motion.div
                className="bg-white rounded-3xl p-8 shadow-lg w-full max-w-md"
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 50 }}
                transition={{ duration: 0.4, delay: 0.2 }}
              >
                <h3 className="text-3xl font-medium mb-6" style={{ fontFamily: 'Aeonik-Medium, sans-serif' }}>
                  Subscribe to our newsletter
                </h3>
                
                <div className="relative">
                  <input
                    type="email"
                    placeholder="Your email"
                    className="w-full bg-gray-100 rounded-full px-6 py-4 pr-16 text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    style={{ fontFamily: 'Aeonik-Regular, sans-serif' }}
                  />
                  <motion.button
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-black rounded-full flex items-center justify-center text-white"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M5 12h14M12 5l7 7-7 7"/>
                    </svg>
                  </motion.button>
                </div>
              </motion.div>

              {/* Footer Section */}
              <motion.div
                className="bg-black rounded-t-3xl p-8 w-full max-w-md mt-8"
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 50 }}
                transition={{ duration: 0.4, delay: 0.3 }}
              >
                <div className="flex items-center justify-between text-white">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-white rounded-full"></div>
                    <div className="w-3 h-3 bg-white rounded-full"></div>
                    <span className="text-xl font-medium ml-4" style={{ fontFamily: 'Aeonik-Medium, sans-serif' }}>
                      LABS
                    </span>
                  </div>
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M7 17L17 7M17 7H7M17 7V17"/>
                  </svg>
                </div>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 3D Pool Section - Moved down, full width */}
      <div className="relative z-10 px-6 lg:px-8 pt-16 pb-24">
        <motion.div 
          className="relative w-full h-[300px] lg:h-[500px] bg-gray-900 rounded-3xl overflow-hidden"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <Pool />
        </motion.div>
      </div>

      {/* Bottom Navigation Dots */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20">
        <motion.div 
          className="text-sm text-gray-600 tracking-wider"
          style={{ fontFamily: 'Aeonik-Regular, sans-serif' }}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          SCROLL TO EXPLORE
        </motion.div>
      </div>

      {/* Corner Plus Icons */}
      <div className="absolute bottom-16 left-8 z-20">
        <motion.div 
          className="w-8 h-8 flex items-center justify-center text-2xl text-gray-400 cursor-pointer hover:text-black transition-colors"
          whileHover={{ rotate: 90 }}
          transition={{ duration: 0.3 }}
        >
          +
        </motion.div>
      </div>

      <div className="absolute bottom-16 left-1/3 transform -translate-x-1/2 z-20">
        <motion.div 
          className="w-8 h-8 flex items-center justify-center text-2xl text-gray-400 cursor-pointer hover:text-black transition-colors"
          whileHover={{ rotate: 90 }}
          transition={{ duration: 0.3 }}
        >
          +
        </motion.div>
      </div>

      <div className="absolute bottom-16 right-1/3 transform translate-x-1/2 z-20">
        <motion.div 
          className="w-8 h-8 flex items-center justify-center text-2xl text-gray-400 cursor-pointer hover:text-black transition-colors"
          whileHover={{ rotate: 90 }}
          transition={{ duration: 0.3 }}
        >
          +
        </motion.div>
      </div>

      <div className="absolute bottom-16 right-8 z-20">
        <motion.div 
          className="w-8 h-8 flex items-center justify-center text-2xl text-gray-400 cursor-pointer hover:text-black transition-colors"
          whileHover={{ rotate: 90 }}
          transition={{ duration: 0.3 }}
        >
          +
        </motion.div>
      </div>
    </div>
  );
};

export default Hero;