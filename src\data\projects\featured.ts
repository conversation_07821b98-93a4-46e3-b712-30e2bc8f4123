export interface Project {
  id: number;
  title: string;
  skills: string[];
  image: string;
  description: string;
  bgColor: string;
}

export const projectsData: Project[] = [
  {
    id: 1,
    title: "Devin AI",
    skills: ["WEB", "DESIGN", "DEVELOPMENT", "3D"],
    image: "/api/placeholder/600/400",
    description: "A collaborative AI teammate that revolutionizes development workflows",
    bgColor: "from-slate-800 to-slate-900"
  },
  {
    id: 2,
    title: "Porsche Dream Machine",
    skills: ["CONCEPT", "3D DESIGN", "MOTION DESIGN", "COMPOSITING"],
    image: "/api/placeholder/600/400",
    description: "Surreal 3D visualization exploring the boundaries of imagination",
    bgColor: "from-rose-300 to-rose-400"
  },
  {
    id: 3,
    title: "Neural Interface",
    skills: ["UI/UX", "WEBGL", "INTERACTION", "AI"],
    image: "/api/placeholder/600/400",
    description: "Next-generation interface design for AI-human interaction",
    bgColor: "from-blue-600 to-purple-700"
  },
  {
    id: 4,
    title: "Quantum Dashboard",
    skills: ["DATA VIZ", "REACT", "D3", "ANALYTICS"],
    image: "/api/placeholder/600/400",
    description: "Real-time quantum computing visualization platform",
    bgColor: "from-green-500 to-teal-600"
  }
];