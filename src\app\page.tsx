'use client';

import { useState, useEffect } from 'react'; 
import Loading from "@/components/Loading"; 
import Hero from "@/components/Hero"; 
import Intro from "@/components/Intro"; 
import FeaturedWork from '@/components/Featured';

export default function Home() { 
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Ensure we start at the top of the page
    window.scrollTo(0, 0);
  }, []);

  if (isLoading) { 
    return <Loading onLoadingComplete={() => setIsLoading(false)} />; 
  }

  return ( 
    <div className="bg-white text-black min-h-screen opacity-0 animate-fadeIn" 
         style={{ fontFamily: 'Aeonik-Regular, sans-serif' }}> 
      <Hero /> 
      <Intro /> 
      <FeaturedWork/> 
    </div> 
  ); 
}