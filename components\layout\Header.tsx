'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const menuItems = [
    { href: '/', label: 'Accueil' },
    { 
      href: '/services', 
      label: 'Nos soins et traitements',
      submenu: [
        { href: '/services/esthetique', label: 'Esthéti<PERSON> dentaire' },
        { href: '/services/chirurgie', label: 'Chirurgie buccale' },
        { href: '/services/soins', label: 'Soins d\'exception' }
      ]
    },
    { href: '/gallery', label: 'Photos Avant/Après' },
    { href: '/about', label: 'Découvrir Elone Clinic' },
    { href: '/contact', label: 'Contact' }
  ];

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm shadow-sm">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-20">
          {/* Logo */}
          <Link href="/" className="flex items-center">
            <div className="text-2xl font-bold text-blue-900">
              ELONE CLINIC
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            {menuItems.map((item, index) => (
              <div key={index} className="relative group">
                <Link 
                  href={item.href}
                  className="text-gray-700 hover:text-blue-900 transition-colors duration-200 py-2"
                >
                  {item.label}
                </Link>
                {item.submenu && (
                  <div className="absolute top-full left-0 mt-2 w-64 bg-white shadow-lg rounded-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                    {item.submenu.map((subItem, subIndex) => (
                      <Link
                        key={subIndex}
                        href={subItem.href}
                        className="block px-4 py-3 text-gray-600 hover:text-blue-900 hover:bg-gray-50 transition-colors duration-200"
                      >
                        {subItem.label}
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </nav>

          {/* CTA Button */}
          <div className="hidden lg:flex items-center space-x-4">
            <Link
              href="/contact"
              className="bg-blue-900 text-white px-6 py-2 rounded-full hover:bg-blue-800 transition-colors duration-200"
            >
              Prendre RDV
            </Link>
            <a
              href="tel:0142969494"
              className="text-blue-900 font-semibold hover:text-blue-800 transition-colors duration-200"
            >
              01 42 96 94 94
            </a>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="lg:hidden p-2"
          >
            <div className="w-6 h-6 flex flex-col justify-center items-center">
              <span className={`block w-6 h-0.5 bg-gray-700 transition-all duration-300 ${isMenuOpen ? 'rotate-45 translate-y-1' : ''}`} />
              <span className={`block w-6 h-0.5 bg-gray-700 mt-1 transition-all duration-300 ${isMenuOpen ? 'opacity-0' : ''}`} />
              <span className={`block w-6 h-0.5 bg-gray-700 mt-1 transition-all duration-300 ${isMenuOpen ? '-rotate-45 -translate-y-1' : ''}`} />
            </div>
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="lg:hidden bg-white border-t"
          >
            <nav className="container mx-auto px-4 py-4">
              {menuItems.map((item, index) => (
                <div key={index} className="py-2">
                  <Link
                    href={item.href}
                    className="block text-gray-700 hover:text-blue-900 transition-colors duration-200"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {item.label}
                  </Link>
                  {item.submenu && (
                    <div className="ml-4 mt-2 space-y-2">
                      {item.submenu.map((subItem, subIndex) => (
                        <Link
                          key={subIndex}
                          href={subItem.href}
                          className="block text-gray-600 hover:text-blue-900 transition-colors duration-200"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          {subItem.label}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              ))}
              <div className="pt-4 border-t mt-4">
                <Link
                  href="/contact"
                  className="block bg-blue-900 text-white text-center px-6 py-3 rounded-full hover:bg-blue-800 transition-colors duration-200 mb-3"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Prendre RDV
                </Link>
                <a
                  href="tel:0142969494"
                  className="block text-blue-900 font-semibold text-center hover:text-blue-800 transition-colors duration-200"
                >
                  01 42 96 94 94
                </a>
              </div>
            </nav>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  );
};

export default Header;
